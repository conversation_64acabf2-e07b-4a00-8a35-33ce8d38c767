<?php

namespace LBCDev\OAuthManager\Tests\Feature\Http\Controllers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\GoogleDriveProvider;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class OAuthControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_authorize_redirects_to_oauth_provider()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('getAuthorizationUrl')
            ->andReturn('https://accounts.google.com/o/oauth2/auth?client_id=test');

        // 💡 Bind the mock into the container so getProviderInstance() picks it up
        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.authorize', $service));

        $response->assertRedirect('https://accounts.google.com/o/oauth2/auth?client_id=test');
    }

    public function test_callback_with_valid_code()
    {
        $service = OAuthService::factory()->create([
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->with('test_code')
            ->andReturn([
                'access_token' => 'new_access_token',
                'refresh_token' => 'new_refresh_token',
                'expires_at' => now()->addHour(),
            ]);

        // 💡 Clave para que el controlador use este mock
        $this->app->instance(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect();
        $response->assertSessionHas('success', 'Service authorized successfully!');

        $service->refresh();
        $this->assertEquals('new_access_token', $service->access_token);
    }

    public function test_callback_with_error()
    {
        $service = OAuthService::factory()->create();

        $response = $this->get(route('oauth-manager.callback', $service) . '?error=access_denied&error_description=User denied access');

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization failed: User denied access');
    }

    public function test_callback_without_code()
    {
        $service = OAuthService::factory()->create();

        $response = $this->get(route('oauth-manager.callback', $service));

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization code not received');
    }

    public function test_callback_handles_exception()
    {
        $service = OAuthService::factory()->create();

        $mockProvider = Mockery::mock(GoogleDriveProvider::class);
        $mockProvider->shouldReceive('setProvider')
            ->with(Mockery::type(OAuthService::class))
            ->andReturnSelf();
        $mockProvider->shouldReceive('handleCallback')
            ->andThrow(new \Exception('OAuth error'));

        // Inyectar el mock en el contenedor para que el controlador lo use
        $this->app->instance(GoogleDriveProvider::class, $mockProvider);

        $response = $this->get(route('oauth-manager.callback', $service) . '?code=test_code');

        $response->assertRedirect();
        $response->assertSessionHas('error', 'Authorization failed: OAuth error');
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
