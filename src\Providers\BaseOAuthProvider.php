<?php

namespace LBC<PERSON>ev\OAuthManager\Providers;

use <PERSON>BC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Contracts\OAuthProviderInterface;

abstract class BaseOAuthProvider implements OAuthProviderInterface
{
    protected OAuthService $service;
    protected array $config;

    public function __construct(OAuthService $service)
    {
        $this->service = $service;
        $this->config = config("oauth-manager.services.{$service->service_type}") ?? [];
    }

    protected function getRedirectUri(): string
    {
        return route('oauth-manager.callback', ['service' => $this->service->slug]);
    }

    public function setProvider(OAuthService $service)
    {
        $this->service = $service;
        $this->config = config("oauth-manager.services.{$service->service_type}") ?? [];
        return $this;
    }

    abstract public function getAuthorizationUrl(): string;
    abstract public function handleCallback(string $code): array;
    abstract public function refreshToken(): ?array;
    abstract public function revokeToken(): bool;
    abstract public function testConnection(): bool;
}
